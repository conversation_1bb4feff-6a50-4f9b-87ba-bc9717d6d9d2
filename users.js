document.addEventListener('DOMContentLoaded', function() {
  const tableBody = document.getElementById("usersTableBody");
  const userModal = document.getElementById("userModal");
  const userForm = document.getElementById("userForm");
  const modalTitle = document.getElementById("modalTitle");
  const passwordGroup = document.getElementById("passwordGroup");
  const userId = document.getElementById("userId");
  const username = document.getElementById("username");
  const password = document.getElementById("password");
  const confirmPassword = document.getElementById("confirmPassword");
  const saveUserBtn = document.getElementById("saveUserBtn");
  const addUserBtn = document.getElementById("addUserBtn");
  const closeBtn = document.querySelector(".close");
  const cancelBtn = document.querySelector(".cancel-btn");
  
  let API_URL = localStorage.getItem('serverUrl') || "http://localhost:5500/api";
  let serverRunning = false;
  let allUsers = [];
  let editMode = false;
  
  // تحديث حالة السيرفر
  function updateServerStatus(connected) {
    const statusBtn = document.getElementById('serverStatus');
    const toggleBtn = document.getElementById('toggleServer');
    
    if (connected) {
      statusBtn.textContent = 'حالة السيرفر: متصل';
      statusBtn.className = 'status-btn connected';
      toggleBtn.textContent = 'إيقاف السيرفر';
      serverRunning = true;
      toggleBtn.disabled = false;
    } else {
      statusBtn.textContent = 'حالة السيرفر: غير متصل';
      statusBtn.className = 'status-btn disconnected';
      toggleBtn.textContent = 'تشغيل السيرفر';
      serverRunning = false;
      toggleBtn.disabled = false;
    }
  }

  // التحقق من حالة السيرفر
  async function checkServerStatus() {
    try {
      const response = await fetch(`${API_URL}/status`);
      if (response.ok) {
        updateServerStatus(true);
        loadUsers();
        return true;
      }
    } catch (error) {
      console.error('Server check failed:', error);
      updateServerStatus(false);
      return false;
    }
    return false;
  }

  // تشغيل/إيقاف السيرفر
  async function toggleServer() {
    const toggleBtn = document.getElementById('toggleServer');
    toggleBtn.disabled = true;

    try {
      if (!serverRunning) {
        // محاولة الاتصال بالسيرفر
        const response = await fetch(`${API_URL}/status`);
        if (response.ok) {
          updateServerStatus(true);
          await loadUsers();
        } else {
          throw new Error('فشل الاتصال بالسيرفر');
        }
      } else {
        // إيقاف الاتصال بالسيرفر
        updateServerStatus(false);
        tableBody.innerHTML = '';
        allUsers = [];
      }
    } catch (error) {
      console.error('Server operation failed:', error);
      alert(serverRunning ? 'فشل في إيقاف السيرفر' : 'فشل في تشغيل السيرفر');
      updateServerStatus(serverRunning);
    } finally {
      toggleBtn.disabled = false;
    }
  }

  // تحميل المستخدمين
  async function loadUsers() {
    try {
      // التحقق من التوكن
      const token = localStorage.getItem('token');
      if (!token) {
        window.location.href = 'login.html';
        return;
      }

      const response = await fetch(`${API_URL}/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.status === 401 || response.status === 403) {
        // إذا كان التوكن غير صالح أو ليس لديه صلاحية
        localStorage.removeItem('token');
        localStorage.removeItem('username');
        window.location.href = 'login.html';
        return;
      }

      if (!response.ok) {
        throw new Error('فشل في تحميل المستخدمين');
      }

      const data = await response.json();
      allUsers = data;
      displayUsers(allUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      tableBody.innerHTML = `<tr><td colspan="5" class="error-message">فشل في تحميل المستخدمين: ${error.message}</td></tr>`;
    }
  }

  // عرض المستخدمين في الجدول
  function displayUsers(users) {
    tableBody.innerHTML = '';
    
    if (users.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="5" class="empty-message">لا يوجد مستخدمين</td></tr>';
      return;
    }

    users.forEach(user => {
      const row = document.createElement('tr');
      
      // حساب عدد الصلاحيات النشطة
      let permissionsCount = 0;
      let permissionsList = [];
      
      if (user.permissions) {
        const permissions = typeof user.permissions === 'string' 
          ? JSON.parse(user.permissions) 
          : user.permissions;
          
        for (const [key, value] of Object.entries(permissions)) {
          if (value === true) {
            permissionsCount++;
            permissionsList.push(key);
          }
        }
      }
      
      // تنسيق تاريخ الإنشاء
      const createdDate = new Date(user.created_at);
      const formattedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')}`;
      
      row.innerHTML = `
        <td>${user.id}</td>
        <td>${user.username}</td>
        <td>${formattedDate}</td>
        <td>
          <span class="permissions-count" title="${permissionsList.join(', ')}">
            ${permissionsCount} صلاحيات
          </span>
        </td>
        <td>
          <button class="action-btn edit-btn" data-id="${user.id}" title="تعديل">
            <i class="fas fa-edit"></i>
          </button>
          <button class="action-btn delete-btn" data-id="${user.id}" title="حذف">
            <i class="fas fa-trash-alt"></i>
          </button>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    document.querySelectorAll('.edit-btn').forEach(btn => {
      btn.addEventListener('click', () => editUser(btn.getAttribute('data-id')));
    });

    document.querySelectorAll('.delete-btn').forEach(btn => {
      btn.addEventListener('click', () => deleteUser(btn.getAttribute('data-id')));
    });

    // تطبيق الصلاحيات على أزرار التعديل والحذف (مع استثناء قسم إدارة المستخدمين)
    // قسم إدارة المستخدمين يجب أن يظهر الأزرار دائماً للمستخدمين الذين لديهم صلاحية manage_users
    if (typeof hasPermission === 'function' && hasPermission('manage_users')) {
      // إظهار جميع الأزرار لمن لديه صلاحية إدارة المستخدمين
      return;
    }
    
    // إخفاء الأزرار إذا لم تكن لديه صلاحية إدارة المستخدمين
    if (typeof hasPermission === 'function' && !hasPermission('manage_users')) {
      document.querySelectorAll('#usersTableBody .edit-btn, #usersTableBody .delete-btn').forEach(btn => {
        btn.style.display = 'none';
      });
    }
  }

  // فتح النافذة المنبثقة لإضافة مستخدم جديد
  function openAddUserModal() {
    editMode = false;
    modalTitle.textContent = 'إضافة مستخدم جديد';
    userForm.reset();
    userId.value = '';
    passwordGroup.style.display = 'block';
    password.required = true;
    confirmPassword.required = true;

    // تعيين الصلاحيات الافتراضية
    document.getElementById('can_view').checked = true;

    // إظهار النافذة مع تأثير الحركة
    userModal.style.display = 'block';
    setTimeout(() => {
      userModal.classList.add('show');
      // إعداد التبويبات بعد ظهور النافذة
      setupPermissionsTabs();
      // إظهار قسم معلومات المستخدم افتراضياً للمستخدم الجديد
      showUserInfoSection();
    }, 10);
  }

  // فتح النافذة المنبثقة لتعديل مستخدم
  function editUser(id) {
    editMode = true;
    const user = allUsers.find(u => u.id == id);
    
    if (!user) {
      alert('المستخدم غير موجود');
      return;
    }
    
    modalTitle.textContent = 'تعديل المستخدم';
    userId.value = user.id;
    username.value = user.username;
    passwordGroup.style.display = 'block';
    password.required = false;
    confirmPassword.required = false;
    password.placeholder = 'اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور';
    confirmPassword.placeholder = 'تأكيد كلمة المرور الجديدة (اختياري)';
    
    // تعيين الصلاحيات
    const permissions = typeof user.permissions === 'string' 
      ? JSON.parse(user.permissions) 
      : user.permissions || {};
    
    // إعادة تعيين جميع الصلاحيات
    document.querySelectorAll('.permission-item input[type="checkbox"]').forEach(checkbox => {
      checkbox.checked = false;
    });
    
    // تحويل الصلاحيات التفصيلية إلى صلاحيات مبسطة
    const simplifiedPermissions = {
      ...permissions
    };

    // تحديد صلاحية المكافآت (تشمل العرض)
    if (permissions.view_rewards_list || permissions.add_reward) {
      simplifiedPermissions.rewards_access = true;
    }

    // تحديد صلاحية الخصومات (تشمل العرض)
    if (permissions.view_deductions_list || permissions.add_deduction) {
      simplifiedPermissions.deductions_access = true;
    }

    // تحديد صلاحية التقارير (بناءً على الصلاحية المخصصة للتقارير)
    if (permissions.view_rewards_deductions_reports) {
      simplifiedPermissions.rewards_reports = true;
    }

    // معالجة صلاحيات الاستقالات - تحويل الصلاحيات التفصيلية إلى مبسطة
    // جميع صلاحيات الاستقالات موجودة بشكل مباشر، لا حاجة لتحويل

    // تعيين الصلاحيات الحالية
    for (const [key, value] of Object.entries(simplifiedPermissions)) {
      const checkbox = document.getElementById(key);
      if (checkbox) {
        checkbox.checked = value === true;
      }
    }
    
    // إظهار النافذة مع تأثير الحركة
    userModal.style.display = 'block';
    setTimeout(() => {
      userModal.classList.add('show');
      // إعداد التبويبات بعد ظهور النافذة
      setupPermissionsTabs();
      // إخفاء قسم معلومات المستخدم افتراضياً لتعديل المستخدم لإفساح مجال أكبر للصلاحيات
      hideUserInfoSection();
    }, 10);
  }

  // حذف مستخدم
  async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      return;
    }
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/users/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف المستخدم');
      }
      
      // تحديث القائمة
      loadUsers();
      alert('تم حذف المستخدم بنجاح');
    } catch (error) {
      console.error('Error deleting user:', error);
      alert(`فشل في حذف المستخدم: ${error.message}`);
    }
  }

  // حفظ المستخدم (إضافة أو تعديل)
  async function saveUser(event) {
    event.preventDefault();
    
    // التحقق من صحة النموذج
    if (!userForm.checkValidity()) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }
    
    // التحقق من تطابق كلمة المرور
    if (password.value || confirmPassword.value) {
      if (password.value !== confirmPassword.value) {
        alert('كلمة المرور وتأكيدها غير متطابقين');
        return;
      }
    }

    // التحقق من وجود كلمة المرور في حالة الإضافة
    if (!editMode && !password.value) {
      alert('كلمة المرور مطلوبة للمستخدم الجديد');
      return;
    }
    
    // جمع الصلاحيات
    const permissions = {};
    document.querySelectorAll('.permission-item input[type="checkbox"]').forEach(checkbox => {
      permissions[checkbox.id] = checkbox.checked;
    });

    // تحويل الصلاحيات المبسطة إلى صلاحيات تفصيلية
    if (permissions.rewards_access) {
      permissions.view_rewards_deductions = true;  // صلاحية الوصول للصفحة
      permissions.view_rewards_list = true;  // العرض أساسي
      permissions.add_reward = true;
      permissions.export_rewards = true;
    }

    if (permissions.deductions_access) {
      permissions.view_rewards_deductions = true;  // صلاحية الوصول للصفحة
      permissions.view_deductions_list = true;  // العرض أساسي
      permissions.add_deduction = true;
      permissions.export_deductions = true;
    }

    if (permissions.rewards_reports) {
      permissions.view_rewards_deductions_reports = true;  // صلاحية التقارير
      // إذا كان المستخدم يريد التقارير فقط، نحتاج صلاحيات العرض على الأقل
      if (!permissions.view_rewards_list && !permissions.view_deductions_list) {
        permissions.view_rewards_deductions = true;  // صلاحية الوصول للصفحة
        permissions.view_rewards_list = true;
        permissions.view_deductions_list = true;
      }
    }

    // معالجة صلاحيات السلف - لا حاجة لتحويل إضافي
    // صلاحيات السلف تُحفظ كما هي:
    // view_salary_advances, add_salary_advance, edit_salary_advance, delete_salary_advance, view_salary_advance_reports

    // معالجة صلاحيات الإضافي - لا حاجة لتحويل إضافي
    // صلاحيات الإضافي تُحفظ كما هي:
    // view_extra_hours, add_extra_hour, edit_extra_hour, delete_extra_hour, view_extra_hour_reports

    // معالجة صلاحيات الاستقالات - لا حاجة لتحويل إضافي
    // صلاحيات الاستقالات تُحفظ كما هي:
    // view_resignations, add_resignation, edit_resignation, delete_resignation, view_resignation_reports

    // ضمان وجود صلاحية الوصول للصفحة عند الحاجة
    const hasAnyRewardsDeductionsPermission = permissions.view_rewards_list ||
                                             permissions.view_deductions_list ||
                                             permissions.add_reward ||
                                             permissions.add_deduction ||
                                             permissions.view_rewards_deductions_reports;

    if (hasAnyRewardsDeductionsPermission && !permissions.view_rewards_deductions) {
      permissions.view_rewards_deductions = true;
    }

    // إعداد بيانات المستخدم
    const userData = {
      username: username.value,
      permissions
    };
    
    // إضافة كلمة المرور في حالة الإضافة أو تغييرها
    if (!editMode || (editMode && password.value.trim())) {
      userData.password = password.value;
    }
    
    try {
      const token = localStorage.getItem('token');
      const url = editMode ? `${API_URL}/users/${userId.value}` : `${API_URL}/users`;
      const method = editMode ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حفظ المستخدم');
      }
      
      // تحديث القائمة وإغلاق النافذة
      loadUsers();
      closeModal();
      alert(editMode ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح');
    } catch (error) {
      console.error('Error saving user:', error);
      alert(`فشل في حفظ المستخدم: ${error.message}`);
    }
  }

  // إغلاق النافذة المنبثقة
  function closeModal() {
    userModal.classList.remove('show');
    setTimeout(() => {
      userModal.style.display = 'none';
      userForm.reset();
    }, 300);
  }

  // إضافة مستمعي الأحداث
  addUserBtn.addEventListener('click', openAddUserModal);
  saveUserBtn.addEventListener('click', saveUser);
  closeBtn.addEventListener('click', closeModal);
  cancelBtn.addEventListener('click', closeModal);
  document.getElementById('toggleServer').addEventListener('click', toggleServer);
  
  // إغلاق النافذة عند النقر خارجها
  window.addEventListener('click', (event) => {
    if (event.target === userModal) {
      closeModal();
    }
  });

  // التحقق من حالة السيرفر عند تحميل الصفحة
  checkServerStatus();

  // تحميل المستخدمين عند تحميل الصفحة
  loadUsers();
});

// إعداد نظام الأقسام للصلاحيات
function setupPermissionsTabs() {
  // لا نحتاج لإعداد التبويبات بعد الآن، النظام الجديد يعتمد على الأقسام
  console.log('تم تحميل نظام الصلاحيات الجديد');
}

// دوال التحكم في الأقسام
function toggleSection(sectionName) {
  const sectionContent = document.getElementById(sectionName + '-section');
  const toggleButton = event.target.closest('.section-toggle');

  if (sectionContent && toggleButton) {
    sectionContent.classList.toggle('collapsed');
    toggleButton.classList.toggle('collapsed');
  }
}

function toggleSectionPermissions(sectionName) {
  const checkboxes = document.querySelectorAll(`input[data-section="${sectionName}"]`);
  const allChecked = Array.from(checkboxes).every(cb => cb.checked);

  checkboxes.forEach(checkbox => {
    checkbox.checked = !allChecked;
  });

  // تحديث نص الزر
  const button = event.target;
  button.textContent = allChecked ? 'تحديد الكل' : 'إلغاء الكل';
}

// دوال التحكم العامة في الصلاحيات
function selectAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-item input[type="checkbox"]');
  checkboxes.forEach(checkbox => {
    checkbox.checked = true;
  });

  // تحديث جميع أزرار الأقسام
  document.querySelectorAll('.toggle-all-btn').forEach(btn => {
    btn.textContent = 'إلغاء الكل';
  });
}

function clearAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-item input[type="checkbox"]');
  checkboxes.forEach(checkbox => {
    checkbox.checked = false;
  });

  // تحديث جميع أزرار الأقسام
  document.querySelectorAll('.toggle-all-btn').forEach(btn => {
    btn.textContent = 'تحديد الكل';
  });
}

function selectViewOnlyPermissions() {
  clearAllPermissions();
  const viewPermissions = [
    'can_view', 'dashboard_access',
    'view_employees', 'view_vacations', 'view_contributions',
    'view_rewards_deductions', 'view_custody', 'view_evaluation',
    'view_training', 'view_salary_advances', 'view_extra_hours', 'view_resignations',
    'view_activity_log'
  ];
  viewPermissions.forEach(permission => {
    const checkbox = document.getElementById(permission);
    if (checkbox) {
      checkbox.checked = true;
    }
  });
}

function selectBasicPermissions() {
  clearAllPermissions();
  const basicPermissions = [
    'can_view', 'can_add', 'can_edit', 'dashboard_access',
    'view_employees', 'add_employees', 'edit_employees'
  ];
  basicPermissions.forEach(permission => {
    const checkbox = document.getElementById(permission);
    if (checkbox) {
      checkbox.checked = true;
    }
  });
}

function expandAllSections() {
  document.querySelectorAll('.section-content').forEach(content => {
    content.classList.remove('collapsed');
  });
  document.querySelectorAll('.section-toggle').forEach(toggle => {
    toggle.classList.remove('collapsed');
  });
}

function collapseAllSections() {
  document.querySelectorAll('.section-content').forEach(content => {
    content.classList.add('collapsed');
  });
  document.querySelectorAll('.section-toggle').forEach(toggle => {
    toggle.classList.add('collapsed');
  });
}

// دالة إظهار وإخفاء كلمة المرور
function togglePasswordVisibility(inputId) {
  const passwordInput = document.getElementById(inputId);
  const eyeIcon = document.getElementById(inputId + '-eye');

  if (passwordInput && eyeIcon) {
    if (passwordInput.type === 'password') {
      passwordInput.type = 'text';
      eyeIcon.classList.remove('fa-eye');
      eyeIcon.classList.add('fa-eye-slash');
    } else {
      passwordInput.type = 'password';
      eyeIcon.classList.remove('fa-eye-slash');
      eyeIcon.classList.add('fa-eye');
    }
  }
}

// دالة إظهار وإخفاء قسم معلومات المستخدم
function toggleUserInfoSection() {
  const userInfoContent = document.getElementById('userInfoContent');
  const toggleIcon = document.getElementById('user-info-toggle-icon');
  const toggleText = document.getElementById('user-info-toggle-text');

  if (userInfoContent && toggleIcon && toggleText) {
    if (userInfoContent.classList.contains('hidden')) {
      // إظهار القسم
      userInfoContent.classList.remove('hidden');
      toggleIcon.classList.remove('fa-eye');
      toggleIcon.classList.add('fa-eye-slash');
      toggleText.textContent = 'إخفاء';
    } else {
      // إخفاء القسم
      userInfoContent.classList.add('hidden');
      toggleIcon.classList.remove('fa-eye-slash');
      toggleIcon.classList.add('fa-eye');
      toggleText.textContent = 'إظهار';
    }
  }
}

// دوال مساعدة لإظهار وإخفاء قسم معلومات المستخدم
function showUserInfoSection() {
  const userInfoContent = document.getElementById('userInfoContent');
  const toggleIcon = document.getElementById('user-info-toggle-icon');
  const toggleText = document.getElementById('user-info-toggle-text');

  if (userInfoContent && toggleIcon && toggleText) {
    userInfoContent.classList.remove('hidden');
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
    toggleText.textContent = 'إخفاء';
  }
}

function hideUserInfoSection() {
  const userInfoContent = document.getElementById('userInfoContent');
  const toggleIcon = document.getElementById('user-info-toggle-icon');
  const toggleText = document.getElementById('user-info-toggle-text');

  if (userInfoContent && toggleIcon && toggleText) {
    userInfoContent.classList.add('hidden');
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
    toggleText.textContent = 'إظهار';
  }
}

// جعل الدوال متاحة عالمياً
window.togglePasswordVisibility = togglePasswordVisibility;
window.toggleUserInfoSection = toggleUserInfoSection;